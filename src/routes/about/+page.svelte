<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;

	const technologies = [
		{ name: 'JavaScript/TypeScript', category: 'Languages', level: 95 },
		{ name: 'Python', category: 'Languages', level: 85 },
		{ name: 'Rust', category: 'Languages', level: 70 },
		{ name: 'SvelteKit', category: 'Frontend', level: 90 },
		{ name: 'React/Next.js', category: 'Frontend', level: 88 },
		{ name: 'Vue.js', category: 'Frontend', level: 82 },
		{ name: 'Three.js/WebGL', category: 'Graphics', level: 85 },
		{ name: 'Node.js', category: 'Backend', level: 87 },
		{ name: 'PostgreSQL', category: 'Database', level: 80 },
		{ name: 'Docker', category: 'DevOps', level: 75 }
	];

	const experiences = [
		{
			year: '2023 - Present',
			title: 'Senior Full-Stack Developer',
			company: 'Tech Innovation Co.',
			description: 'Leading development of cutting-edge web applications using modern frameworks and 3D technologies.'
		},
		{
			year: '2021 - 2023',
			title: 'Frontend Developer',
			company: 'Digital Solutions Ltd.',
			description: 'Specialized in creating responsive, interactive user interfaces and implementing complex animations.'
		},
		{
			year: '2019 - 2021',
			title: 'Junior Developer',
			company: 'StartUp Ventures',
			description: 'Gained experience in full-stack development, working on various projects from concept to deployment.'
		}
	];

	onMount(async () => {
		if (browser && canvasElement) {
			const { AvatarScene } = await import('$lib/three/scenes/AvatarScene');
			scene = new AvatarScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});
</script>

<svelte:head>
	<title>About Me - My Portfolio</title>
	<meta name="description" content="Learn about my journey as a developer, my background, skills, and passion for creating innovative digital experiences." />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="fixed top-0 left-0 w-full h-full -z-10"
	style="pointer-events: none;"
></canvas>

<!-- About Section -->
<section class="relative min-h-screen pt-24 pb-20 px-4 sm:px-6 lg:px-8">
	<div class="max-w-6xl mx-auto">
		<!-- Header -->
		<div class="text-center text-white mb-16">
			<h1 class="text-4xl md:text-6xl font-bold mb-6">
				<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
					About Me
				</span>
			</h1>
			<p class="text-xl text-gray-300 max-w-3xl mx-auto">
				Passionate developer with a love for creating immersive digital experiences
				and pushing the boundaries of web technology.
			</p>
		</div>

		<!-- Story Section -->
		<div class="grid lg:grid-cols-2 gap-12 mb-20">
			<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8">
				<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
					<span class="w-2 h-8 bg-blue-400 mr-4"></span>
					My Journey
				</h2>
				<div class="text-gray-300 space-y-4">
					<p>
						My journey into development began during my computer science studies, where I discovered
						the perfect blend of creativity and logic that programming offers. What started as curiosity
						about how websites work quickly evolved into a passion for creating digital experiences
						that inspire and engage users.
					</p>
					<p>
						Over the years, I've had the privilege of working on diverse projects, from e-commerce
						platforms to interactive 3D applications. Each project has taught me something new and
						reinforced my belief that technology should be both powerful and accessible.
					</p>
					<p>
						Today, I specialize in modern web technologies, with a particular focus on creating
						visually stunning and highly interactive applications. I'm constantly exploring new
						technologies and techniques to stay at the forefront of web development.
					</p>
				</div>
			</div>

			<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8">
				<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
					<span class="w-2 h-8 bg-purple-400 mr-4"></span>
					What Drives Me
				</h2>
				<div class="text-gray-300 space-y-4">
					<p>
						I'm passionate about the intersection of technology and creativity. There's something
						magical about transforming an idea into a living, breathing digital experience that
						users can interact with and enjoy.
					</p>
					<p>
						My approach to development is user-centered and performance-focused. I believe that
						great software should not only look beautiful but also be fast, accessible, and
						intuitive to use. Every line of code I write is guided by these principles.
					</p>
					<p>
						When I'm not coding, you'll find me exploring new technologies, contributing to
						open-source projects, or experimenting with 3D graphics and interactive animations.
						I'm always eager to learn and share knowledge with the developer community.
					</p>
				</div>
			</div>
		</div>

		<!-- Experience Timeline -->
		<div class="mb-20">
			<h2 class="text-2xl font-bold text-white mb-8 flex items-center">
				<span class="w-2 h-8 bg-cyan-400 mr-4"></span>
				Professional Experience
			</h2>

			<div class="space-y-8">
				{#each experiences as exp, index}
					<div class="flex gap-6 group">
						<div class="flex-shrink-0 w-24 text-right">
							<span class="text-blue-400 font-semibold text-sm">{exp.year}</span>
						</div>
						<div class="flex-shrink-0 w-4 flex flex-col items-center">
							<div class="w-4 h-4 bg-blue-400 rounded-full group-hover:bg-purple-400 transition-colors"></div>
							{#if index < experiences.length - 1}
								<div class="w-0.5 h-16 bg-gray-600 mt-2"></div>
							{/if}
						</div>
						<div class="flex-1 bg-gray-800/60 backdrop-blur-sm rounded-lg p-6 group-hover:bg-gray-700/60 transition-all">
							<h3 class="text-xl font-bold text-white mb-1">{exp.title}</h3>
							<p class="text-blue-400 mb-3">{exp.company}</p>
							<p class="text-gray-300">{exp.description}</p>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Technologies -->
		<div class="mb-20">
			<h2 class="text-2xl font-bold text-white mb-8 flex items-center">
				<span class="w-2 h-8 bg-green-400 mr-4"></span>
				Technologies I Love
			</h2>

			<div class="grid md:grid-cols-2 gap-6">
				{#each technologies as tech}
					<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 hover:bg-gray-700/60 transition-all">
						<div class="flex justify-between items-center mb-2">
							<span class="text-white font-medium">{tech.name}</span>
							<span class="text-blue-400 text-sm">{tech.category}</span>
						</div>
						<div class="w-full bg-gray-700 rounded-full h-2">
							<div
								class="bg-gradient-to-r from-blue-400 to-purple-500 h-2 rounded-full transition-all duration-1000"
								style="width: {tech.level}%"
							></div>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Personal Interests -->
		<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8">
			<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
				<span class="w-2 h-8 bg-yellow-400 mr-4"></span>
				Beyond Code
			</h2>

			<div class="grid md:grid-cols-3 gap-6 text-gray-300">
				<div class="text-center">
					<div class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
							<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
						</svg>
					</div>
					<h3 class="font-semibold text-white mb-2">3D Graphics</h3>
					<p class="text-sm">Exploring the possibilities of WebGL and Three.js to create immersive experiences.</p>
				</div>

				<div class="text-center">
					<div class="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
							<path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
						</svg>
					</div>
					<h3 class="font-semibold text-white mb-2">Open Source</h3>
					<p class="text-sm">Contributing to the developer community through open source projects and knowledge sharing.</p>
				</div>

				<div class="text-center">
					<div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-green-400" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
						</svg>
					</div>
					<h3 class="font-semibold text-white mb-2">Learning</h3>
					<p class="text-sm">Constantly exploring new technologies and staying updated with the latest industry trends.</p>
				</div>
			</div>
		</div>

		<!-- Call to Action -->
		<div class="text-center mt-16">
			<h3 class="text-2xl font-bold text-white mb-4">Let's Create Something Amazing</h3>
			<p class="text-gray-300 mb-8 max-w-2xl mx-auto">
				I'm always excited to collaborate on innovative projects and bring creative ideas to life.
				Whether you have a specific vision or need guidance on your next digital venture, I'd love to help.
			</p>
			<a
				href="/contact"
				class="inline-block px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
			>
				Get In Touch
			</a>
		</div>
	</div>
</section>
