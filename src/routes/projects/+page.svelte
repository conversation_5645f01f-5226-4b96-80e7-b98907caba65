<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;
	let scrollContainer: HTMLElement;

	const projects = [
		{
			id: 1,
			title: 'E-Commerce Platform',
			description: 'A modern e-commerce solution built with SvelteKit, featuring real-time inventory management, payment processing, and an intuitive admin dashboard.',
			technologies: ['SvelteKit', 'TypeScript', 'Tailwind CSS', 'Stripe', 'PostgreSQL'],
			image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop',
			demoUrl: 'https://demo.example.com',
			githubUrl: 'https://github.com/yourusername/ecommerce',
			featured: true
		},
		{
			id: 2,
			title: '3D Portfolio Website',
			description: 'An immersive portfolio website showcasing Three.js capabilities with interactive 3D elements, particle systems, and scroll-based animations.',
			technologies: ['Three.js', 'Svelte', 'WebGL', 'GLSL', 'Vite'],
			image: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=600&h=400&fit=crop',
			demoUrl: 'https://portfolio.example.com',
			githubUrl: 'https://github.com/yourusername/3d-portfolio',
			featured: true
		},
		{
			id: 3,
			title: 'Task Management App',
			description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
			technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB', 'Express'],
			image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop',
			demoUrl: 'https://tasks.example.com',
			githubUrl: 'https://github.com/yourusername/task-manager',
			featured: false
		},
		{
			id: 4,
			title: 'Weather Dashboard',
			description: 'A beautiful weather dashboard with interactive maps, detailed forecasts, and location-based weather alerts using modern APIs.',
			technologies: ['Vue.js', 'D3.js', 'OpenWeather API', 'Mapbox', 'Chart.js'],
			image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=600&h=400&fit=crop',
			demoUrl: 'https://weather.example.com',
			githubUrl: 'https://github.com/yourusername/weather-dashboard',
			featured: false
		},
		{
			id: 5,
			title: 'AI Chat Interface',
			description: 'An intelligent chat interface powered by AI, featuring natural language processing, context awareness, and multi-language support.',
			technologies: ['Next.js', 'OpenAI API', 'Prisma', 'TailwindCSS', 'Vercel'],
			image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=400&fit=crop',
			demoUrl: 'https://chat.example.com',
			githubUrl: 'https://github.com/yourusername/ai-chat',
			featured: true
		},
		{
			id: 6,
			title: 'Music Streaming App',
			description: 'A responsive music streaming application with playlist management, audio visualization, and social sharing features.',
			technologies: ['Angular', 'Web Audio API', 'Firebase', 'RxJS', 'Material UI'],
			image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=400&fit=crop',
			demoUrl: 'https://music.example.com',
			githubUrl: 'https://github.com/yourusername/music-app',
			featured: false
		}
	];

	onMount(async () => {
		if (browser && canvasElement) {
			const { ParticleScene } = await import('$lib/three/scenes/ParticleScene');
			scene = new ParticleScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});

	const scrollLeft = () => {
		scrollContainer.scrollBy({ left: -400, behavior: 'smooth' });
	};

	const scrollRight = () => {
		scrollContainer.scrollBy({ left: 400, behavior: 'smooth' });
	};
</script>

<svelte:head>
	<title>Projects - My Portfolio</title>
	<meta name="description" content="Explore my latest projects and development work showcasing modern web technologies and creative solutions." />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="fixed top-0 left-0 w-full h-full -z-10"
	style="pointer-events: none;"
></canvas>

<!-- Projects Section -->
<section class="relative min-h-screen pt-24 pb-20 px-4 sm:px-6 lg:px-8">
	<div class="max-w-7xl mx-auto">
		<!-- Header -->
		<div class="text-center text-white mb-16">
			<h1 class="text-4xl md:text-6xl font-bold mb-6">
				<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
					My Projects
				</span>
			</h1>
			<p class="text-xl text-gray-300 max-w-3xl mx-auto">
				A collection of my recent work showcasing various technologies and creative solutions.
				Each project represents a unique challenge and learning experience.
			</p>
		</div>

		<!-- Featured Projects -->
		<div class="mb-16">
			<h2 class="text-2xl font-bold text-white mb-8 flex items-center">
				<span class="w-2 h-8 bg-blue-400 mr-4"></span>
				Featured Projects
			</h2>

			<div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
				{#each projects.filter(p => p.featured) as project}
					<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg overflow-hidden hover:bg-gray-700/60 transition-all duration-300 transform hover:scale-105">
						<div class="aspect-video bg-gray-800 overflow-hidden">
							<img
								src={project.image}
								alt={project.title}
								class="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
							/>
						</div>
						<div class="p-6">
							<h3 class="text-xl font-bold text-white mb-2">{project.title}</h3>
							<p class="text-gray-300 mb-4 text-sm">{project.description}</p>
							<div class="flex flex-wrap gap-2 mb-4">
								{#each project.technologies as tech}
									<span class="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full">
										{tech}
									</span>
								{/each}
							</div>
							<div class="flex gap-3">
								<a
									href={project.demoUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="px-4 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
								>
									Live Demo
								</a>
								<a
									href={project.githubUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="px-4 py-2 border border-gray-400 text-gray-300 text-sm rounded hover:bg-gray-400 hover:text-black transition-colors"
								>
									GitHub
								</a>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- All Projects - Horizontal Scroll -->
		<div class="mb-16">
			<div class="flex items-center justify-between mb-8">
				<h2 class="text-2xl font-bold text-white flex items-center">
					<span class="w-2 h-8 bg-purple-400 mr-4"></span>
					All Projects
				</h2>
				<div class="flex gap-2">
					<button
						on:click={scrollLeft}
						class="p-2 bg-gray-800/60 text-white rounded-full hover:bg-gray-700/60 transition-colors"
						aria-label="Scroll left"
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
						</svg>
					</button>
					<button
						on:click={scrollRight}
						class="p-2 bg-gray-800/60 text-white rounded-full hover:bg-gray-700/60 transition-colors"
						aria-label="Scroll right"
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
						</svg>
					</button>
				</div>
			</div>

			<div
				bind:this={scrollContainer}
				class="flex gap-6 overflow-x-auto scrollbar-hide pb-4"
				style="scroll-snap-type: x mandatory;"
			>
				{#each projects as project}
					<div class="flex-none w-80 bg-gray-800/60 backdrop-blur-sm rounded-lg overflow-hidden hover:bg-gray-700/60 transition-all duration-300" style="scroll-snap-align: start;">
						<div class="aspect-video bg-gray-800 overflow-hidden">
							<img
								src={project.image}
								alt={project.title}
								class="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
							/>
						</div>
						<div class="p-6">
							<h3 class="text-lg font-bold text-white mb-2">{project.title}</h3>
							<p class="text-gray-300 mb-4 text-sm line-clamp-3">{project.description}</p>
							<div class="flex flex-wrap gap-1 mb-4">
								{#each project.technologies.slice(0, 3) as tech}
									<span class="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full">
										{tech}
									</span>
								{/each}
								{#if project.technologies.length > 3}
									<span class="px-2 py-1 bg-gray-500/20 text-gray-300 text-xs rounded-full">
										+{project.technologies.length - 3}
									</span>
								{/if}
							</div>
							<div class="flex gap-2">
								<a
									href={project.demoUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="flex-1 px-3 py-2 bg-blue-500 text-white text-sm rounded text-center hover:bg-blue-600 transition-colors"
								>
									Demo
								</a>
								<a
									href={project.githubUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="flex-1 px-3 py-2 border border-gray-400 text-gray-300 text-sm rounded text-center hover:bg-gray-400 hover:text-black transition-colors"
								>
									Code
								</a>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Call to Action -->
		<div class="text-center">
			<h3 class="text-2xl font-bold text-white mb-4">Interested in Working Together?</h3>
			<p class="text-gray-300 mb-8 max-w-2xl mx-auto">
				I'm always excited to take on new challenges and collaborate on innovative projects.
				Let's discuss how we can bring your ideas to life.
			</p>
			<a
				href="/contact"
				class="inline-block px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
			>
				Get In Touch
			</a>
		</div>
	</div>
</section>

<style>
	.scrollbar-hide {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}

	.scrollbar-hide::-webkit-scrollbar {
		display: none;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
