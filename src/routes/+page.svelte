<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;

	onMount(async () => {
		if (browser && canvasElement) {
			const { RotatingPrimitivesScene } = await import('$lib/three/scenes/RotatingPrimitivesScene');
			scene = new RotatingPrimitivesScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});

	const handleExploreClick = () => {
		goto('/projects');
	};

	const handleAboutClick = () => {
		goto('/about');
	};
</script>

<svelte:head>
	<title>Portfolio - Creative Developer</title>
	<meta name="description" content="Welcome to my portfolio. I'm a passionate developer creating innovative solutions with modern technologies." />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="fixed top-0 left-0 w-full h-full -z-10"
	style="pointer-events: none;"
></canvas>

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
	<div class="text-center text-white max-w-4xl mx-auto">
		<h1 class="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
			<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
				Creative Developer
			</span>
		</h1>

		<p class="text-xl md:text-2xl mb-8 text-gray-300 animate-fade-in-delay-1">
			Crafting immersive digital experiences with cutting-edge technologies
		</p>

		<p class="text-lg mb-12 text-gray-400 max-w-2xl mx-auto animate-fade-in-delay-2">
			I specialize in creating visually stunning and highly interactive web applications
			using modern frameworks, 3D graphics, and innovative design patterns.
		</p>

		<div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-delay-3">
			<button
				on:click={handleExploreClick}
				class="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
			>
				Explore My Work
			</button>

			<button
				on:click={handleAboutClick}
				class="px-8 py-4 border-2 border-blue-400 text-blue-400 font-semibold rounded-lg hover:bg-blue-400 hover:text-white transition-all duration-300 transform hover:scale-105"
			>
				About Me
			</button>
		</div>
	</div>
</section>

<!-- Introduction Section -->
<section class="relative py-20 px-4 sm:px-6 lg:px-8 bg-black/50 backdrop-blur-sm">
	<div class="max-w-4xl mx-auto text-center text-white">
		<h2 class="text-3xl md:text-4xl font-bold mb-8">
			<span class="text-blue-400">Passionate</span> About Innovation
		</h2>

		<div class="grid md:grid-cols-3 gap-8 mb-12">
			<div class="p-6 bg-white/10 rounded-lg backdrop-blur-sm">
				<div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-4">
					<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
						<path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
					</svg>
				</div>
				<h3 class="text-xl font-semibold mb-2">User-Centered Design</h3>
				<p class="text-gray-300">Creating intuitive and engaging user experiences that delight and inspire.</p>
			</div>

			<div class="p-6 bg-white/10 rounded-lg backdrop-blur-sm">
				<div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4">
					<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
					</svg>
				</div>
				<h3 class="text-xl font-semibold mb-2">Modern Technologies</h3>
				<p class="text-gray-300">Leveraging the latest tools and frameworks to build scalable solutions.</p>
			</div>

			<div class="p-6 bg-white/10 rounded-lg backdrop-blur-sm">
				<div class="w-12 h-12 bg-cyan-500 rounded-lg flex items-center justify-center mx-auto mb-4">
					<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
						<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
					</svg>
				</div>
				<h3 class="text-xl font-semibold mb-2">Quality Focused</h3>
				<p class="text-gray-300">Committed to delivering high-quality, performant, and maintainable code.</p>
			</div>
		</div>

		<button
			on:click={() => goto('/contact')}
			class="px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-semibold rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
		>
			Let's Work Together
		</button>
	</div>
</section>

<style>
	@keyframes fade-in {
		from {
			opacity: 0;
			transform: translateY(30px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animate-fade-in {
		animation: fade-in 1s ease-out;
	}

	.animate-fade-in-delay-1 {
		animation: fade-in 1s ease-out 0.2s both;
	}

	.animate-fade-in-delay-2 {
		animation: fade-in 1s ease-out 0.4s both;
	}

	.animate-fade-in-delay-3 {
		animation: fade-in 1s ease-out 0.6s both;
	}
</style>
