<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;

	const technicalSkills = [
		{ name: 'JavaScript/TypeScript', level: 95, category: 'Languages' },
		{ name: 'Python', level: 85, category: 'Languages' },
		{ name: 'Rust', level: 70, category: 'Languages' },
		{ name: 'Go', level: 65, category: 'Languages' },
		{ name: 'SvelteKit', level: 90, category: 'Frontend' },
		{ name: 'React/Next.js', level: 88, category: 'Frontend' },
		{ name: 'Vue.js/Nuxt', level: 82, category: 'Frontend' },
		{ name: 'Angular', level: 75, category: 'Frontend' },
		{ name: 'Three.js/WebGL', level: 85, category: 'Graphics' },
		{ name: 'D3.js', level: 78, category: 'Graphics' },
		{ name: 'Node.js', level: 87, category: 'Backend' },
		{ name: 'Express/Fastify', level: 85, category: 'Backend' },
		{ name: 'PostgreSQL', level: 80, category: 'Database' },
		{ name: 'MongoDB', level: 75, category: 'Database' },
		{ name: 'Redis', level: 70, category: 'Database' },
		{ name: 'Docker', level: 75, category: 'DevOps' },
		{ name: 'AWS/GCP', level: 72, category: 'DevOps' },
		{ name: 'Git/GitHub', level: 90, category: 'Tools' }
	];

	const softSkills = [
		{ name: 'Problem Solving', description: 'Breaking down complex challenges into manageable solutions' },
		{ name: 'Team Collaboration', description: 'Working effectively with cross-functional teams' },
		{ name: 'Communication', description: 'Clearly explaining technical concepts to diverse audiences' },
		{ name: 'Adaptability', description: 'Quickly learning new technologies and adapting to change' },
		{ name: 'Leadership', description: 'Mentoring junior developers and leading technical initiatives' },
		{ name: 'Creative Thinking', description: 'Approaching problems from unique angles and innovative solutions' }
	];

	const categories = [...new Set(technicalSkills.map(skill => skill.category))];

	onMount(async () => {
		if (browser && canvasElement) {
			const { CylinderScene } = await import('$lib/three/scenes/CylinderScene');
			scene = new CylinderScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});

	const getSkillsByCategory = (category: string) => {
		return technicalSkills.filter(skill => skill.category === category);
	};

	const getCategoryColor = (category: string) => {
		const colors = {
			'Languages': 'from-blue-500 to-blue-600',
			'Frontend': 'from-purple-500 to-purple-600',
			'Backend': 'from-green-500 to-green-600',
			'Database': 'from-yellow-500 to-yellow-600',
			'Graphics': 'from-pink-500 to-pink-600',
			'DevOps': 'from-cyan-500 to-cyan-600',
			'Tools': 'from-orange-500 to-orange-600'
		};
		return colors[category] || 'from-gray-500 to-gray-600';
	};
</script>

<svelte:head>
	<title>Skills - My Portfolio</title>
	<meta name="description" content="Explore my technical skills, expertise in modern web technologies, and soft skills that drive successful project delivery." />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="fixed top-0 left-0 w-full h-full -z-10"
	style="pointer-events: none;"
></canvas>

<!-- Skills Section -->
<section class="relative min-h-screen pt-24 pb-20 px-4 sm:px-6 lg:px-8">
	<div class="max-w-7xl mx-auto">
		<!-- Header -->
		<div class="text-center text-white mb-16">
			<h1 class="text-4xl md:text-6xl font-bold mb-6">
				<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
					My Skills
				</span>
			</h1>
			<p class="text-xl text-gray-300 max-w-3xl mx-auto">
				A comprehensive overview of my technical expertise and the soft skills
				that enable me to deliver exceptional results in every project.
			</p>
		</div>

		<!-- Technical Skills -->
		<div class="mb-20">
			<h2 class="text-3xl font-bold text-white mb-12 text-center">Technical Expertise</h2>

			<div class="grid lg:grid-cols-2 gap-8">
				{#each categories as category}
					<div class="bg-gray-800 bg-opacity-60 backdrop-blur-sm rounded-lg p-6 hover:bg-gray-700 hover:bg-opacity-60 transition-all">
						<h3 class="text-xl font-bold text-white mb-6 flex items-center">
							<span class="w-3 h-6 bg-gradient-to-b {getCategoryColor(category)} mr-3 rounded"></span>
							{category}
						</h3>

						<div class="space-y-4">
							{#each getSkillsByCategory(category) as skill}
								<div class="group">
									<div class="flex justify-between items-center mb-2">
										<span class="text-gray-300 group-hover:text-white transition-colors">{skill.name}</span>
										<span class="text-blue-400 text-sm font-medium">{skill.level}%</span>
									</div>
									<div class="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
										<div
											class="bg-gradient-to-r {getCategoryColor(category)} h-2 rounded-full transition-all duration-1000 ease-out"
											style="width: {skill.level}%"
										></div>
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Soft Skills -->
		<div class="mb-20">
			<h2 class="text-3xl font-bold text-white mb-12 text-center">Soft Skills & Qualities</h2>

			<div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
				{#each softSkills as skill, index}
					<div class="bg-gray-800 bg-opacity-60 backdrop-blur-sm rounded-lg p-6 hover:bg-gray-700 hover:bg-opacity-60 transition-all transform hover:scale-105 group">
						<div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
							<span class="text-white font-bold text-lg">{index + 1}</span>
						</div>
						<h3 class="text-lg font-bold text-white mb-3">{skill.name}</h3>
						<p class="text-gray-300 text-sm">{skill.description}</p>
					</div>
				{/each}
			</div>
		</div>

		<!-- Skills Summary -->
		<div class="bg-gray-800 bg-opacity-60 backdrop-blur-sm rounded-lg p-8 mb-16">
			<div class="grid md:grid-cols-3 gap-8 text-center">
				<div>
					<div class="text-3xl font-bold text-blue-400 mb-2">{technicalSkills.length}+</div>
					<div class="text-white font-medium mb-1">Technical Skills</div>
					<div class="text-gray-300 text-sm">Across multiple domains</div>
				</div>
				<div>
					<div class="text-3xl font-bold text-purple-400 mb-2">5+</div>
					<div class="text-white font-medium mb-1">Years Experience</div>
					<div class="text-gray-300 text-sm">In web development</div>
				</div>
				<div>
					<div class="text-3xl font-bold text-cyan-400 mb-2">50+</div>
					<div class="text-white font-medium mb-1">Projects Completed</div>
					<div class="text-gray-300 text-sm">From concept to deployment</div>
				</div>
			</div>
		</div>

		<!-- Learning Philosophy -->
		<div class="bg-gray-800 bg-opacity-60 backdrop-blur-sm rounded-lg p-8 mb-16">
			<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
				<span class="w-2 h-8 bg-green-400 mr-4"></span>
				Continuous Learning
			</h2>

			<div class="grid md:grid-cols-2 gap-8">
				<div>
					<h3 class="text-lg font-semibold text-white mb-4">Currently Exploring</h3>
					<ul class="space-y-2 text-gray-300">
						<li class="flex items-center">
							<span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
							WebAssembly (WASM) for high-performance web apps
						</li>
						<li class="flex items-center">
							<span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
							Advanced Three.js techniques and shaders
						</li>
						<li class="flex items-center">
							<span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
							Machine Learning integration in web applications
						</li>
						<li class="flex items-center">
							<span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
							Edge computing and serverless architectures
						</li>
					</ul>
				</div>

				<div>
					<h3 class="text-lg font-semibold text-white mb-4">Learning Approach</h3>
					<p class="text-gray-300 mb-4">
						I believe in hands-on learning through building real projects. Every new technology
						I explore is immediately applied to practical use cases, ensuring deep understanding
						and retention.
					</p>
					<p class="text-gray-300">
						I actively participate in developer communities, contribute to open source projects,
						and share knowledge through blog posts and mentoring sessions.
					</p>
				</div>
			</div>
		</div>

		<!-- Call to Action -->
		<div class="text-center">
			<h3 class="text-2xl font-bold text-white mb-4">Ready to Collaborate?</h3>
			<p class="text-gray-300 mb-8 max-w-2xl mx-auto">
				Whether you need expertise in modern web technologies, 3D graphics, or full-stack development,
				I'm here to help bring your vision to life with cutting-edge solutions.
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a
					href="/projects"
					class="px-8 py-4 border-2 border-blue-400 text-blue-400 font-semibold rounded-lg hover:bg-blue-400 hover:text-white transition-all duration-300 transform hover:scale-105"
				>
					View My Work
				</a>
				<a
					href="/contact"
					class="px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
				>
					Start a Project
				</a>
			</div>
		</div>
	</div>
</section>
