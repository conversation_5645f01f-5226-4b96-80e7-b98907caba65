<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;
	let formData = {
		name: '',
		email: '',
		subject: '',
		message: ''
	};
	let isSubmitting = false;
	let submitStatus: 'idle' | 'success' | 'error' = 'idle';

	const socialLinks = [
		{
			name: '<PERSON>itH<PERSON>',
			href: 'https://github.com/yourusername',
			icon: 'M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z',
			color: 'hover:text-gray-300'
		},
		{
			name: 'LinkedIn',
			href: 'https://linkedin.com/in/yourusername',
			icon: 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z',
			color: 'hover:text-blue-400'
		},
		{
			name: 'Twitter',
			href: 'https://twitter.com/yourusername',
			icon: 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z',
			color: 'hover:text-blue-400'
		}
	];

	onMount(async () => {
		if (browser && canvasElement) {
			const { WaveScene } = await import('$lib/three/scenes/WaveScene');
			scene = new WaveScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});

	const handleSubmit = async (event: Event) => {
		event.preventDefault();
		isSubmitting = true;
		submitStatus = 'idle';

		try {
			// Simulate form submission
			await new Promise(resolve => setTimeout(resolve, 2000));

			// Here you would typically send the form data to your backend
			console.log('Form submitted:', formData);

			submitStatus = 'success';
			formData = { name: '', email: '', subject: '', message: '' };
		} catch (error) {
			console.error('Form submission error:', error);
			submitStatus = 'error';
		} finally {
			isSubmitting = false;
		}
	};
</script>

<svelte:head>
	<title>Contact - My Portfolio</title>
	<meta name="description" content="Get in touch with me for collaboration opportunities, project inquiries, or just to say hello. Let's create something amazing together." />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="fixed top-0 left-0 w-full h-full -z-10"
	style="pointer-events: none;"
></canvas>

<!-- Contact Section -->
<section class="relative min-h-screen pt-24 pb-20 px-4 sm:px-6 lg:px-8">
	<div class="max-w-6xl mx-auto">
		<!-- Header -->
		<div class="text-center text-white mb-16">
			<h1 class="text-4xl md:text-6xl font-bold mb-6">
				<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
					Let's Connect
				</span>
			</h1>
			<p class="text-xl text-gray-300 max-w-3xl mx-auto">
				Ready to bring your ideas to life? I'm always excited to discuss new projects,
				creative collaborations, or just chat about the latest in web development.
			</p>
		</div>

		<div class="grid lg:grid-cols-2 gap-12">
			<!-- Contact Form -->
			<div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
				<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
					<span class="w-2 h-8 bg-blue-400 mr-4"></span>
					Send a Message
				</h2>

				<form on:submit={handleSubmit} class="space-y-6">
					<div class="grid md:grid-cols-2 gap-4">
						<div>
							<label for="name" class="block text-sm font-medium text-gray-300 mb-2">
								Name *
							</label>
							<input
								type="text"
								id="name"
								bind:value={formData.name}
								required
								class="w-full px-4 py-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 focus:ring-1 focus:ring-blue-400 transition-colors"
								placeholder="Your name"
							/>
						</div>
						<div>
							<label for="email" class="block text-sm font-medium text-gray-300 mb-2">
								Email *
							</label>
							<input
								type="email"
								id="email"
								bind:value={formData.email}
								required
								class="w-full px-4 py-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 focus:ring-1 focus:ring-blue-400 transition-colors"
								placeholder="<EMAIL>"
							/>
						</div>
					</div>

					<div>
						<label for="subject" class="block text-sm font-medium text-gray-300 mb-2">
							Subject *
						</label>
						<input
							type="text"
							id="subject"
							bind:value={formData.subject}
							required
							class="w-full px-4 py-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 focus:ring-1 focus:ring-blue-400 transition-colors"
							placeholder="What's this about?"
						/>
					</div>

					<div>
						<label for="message" class="block text-sm font-medium text-gray-300 mb-2">
							Message *
						</label>
						<textarea
							id="message"
							bind:value={formData.message}
							required
							rows="6"
							class="w-full px-4 py-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 focus:ring-1 focus:ring-blue-400 transition-colors resize-none"
							placeholder="Tell me about your project or idea..."
						></textarea>
					</div>

					<button
						type="submit"
						disabled={isSubmitting}
						class="w-full px-6 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
					>
						{#if isSubmitting}
							<span class="flex items-center justify-center">
								<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
									<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
									<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
								</svg>
								Sending...
							</span>
						{:else}
							Send Message
						{/if}
					</button>

					{#if submitStatus === 'success'}
						<div class="p-4 bg-green-500/20 border border-green-500 rounded-lg text-green-300">
							Thank you! Your message has been sent successfully. I'll get back to you soon.
						</div>
					{/if}

					{#if submitStatus === 'error'}
						<div class="p-4 bg-red-500/20 border border-red-500 rounded-lg text-red-300">
							Sorry, there was an error sending your message. Please try again or contact me directly.
						</div>
					{/if}
				</form>
			</div>

			<!-- Contact Information -->
			<div class="space-y-8">
				<!-- Direct Contact -->
				<div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
					<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
						<span class="w-2 h-8 bg-purple-400 mr-4"></span>
						Get In Touch
					</h2>

					<div class="space-y-4">
						<div class="flex items-center space-x-4">
							<div class="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
								<svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
									<path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
									<path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
								</svg>
							</div>
							<div>
								<p class="text-white font-medium">Email</p>
								<a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition-colors">
									<EMAIL>
								</a>
							</div>
						</div>

						<div class="flex items-center space-x-4">
							<div class="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
								<svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
								</svg>
							</div>
							<div>
								<p class="text-white font-medium">Phone</p>
								<a href="tel:+15551234567" class="text-green-400 hover:text-green-300 transition-colors">
									+****************
								</a>
							</div>
						</div>

						<div class="flex items-center space-x-4">
							<div class="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center">
								<svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
								</svg>
							</div>
							<div>
								<p class="text-white font-medium">Location</p>
								<p class="text-gray-300">Your City, Country</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Social Media -->
				<div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
					<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
						<span class="w-2 h-8 bg-cyan-400 mr-4"></span>
						Follow Me
					</h2>

					<div class="flex space-x-4">
						{#each socialLinks as social}
							<a
								href={social.href}
								target="_blank"
								rel="noopener noreferrer"
								class="w-12 h-12 bg-gray-800 rounded-full flex items-center justify-center text-gray-300 {social.color} transition-all duration-200 transform hover:scale-110"
								aria-label={social.name}
							>
								<svg class="w-6 h-6 fill-current" viewBox="0 0 24 24">
									<path d={social.icon} />
								</svg>
							</a>
						{/each}
					</div>

					<p class="text-gray-300 mt-4 text-sm">
						Follow me on social media for updates on my latest projects,
						tech insights, and behind-the-scenes content.
					</p>
				</div>

				<!-- Availability -->
				<div class="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-lg p-8">
					<h3 class="text-lg font-bold text-white mb-4">Current Availability</h3>
					<div class="flex items-center space-x-3 mb-4">
						<div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
						<span class="text-green-400 font-medium">Available for new projects</span>
					</div>
					<p class="text-gray-300 text-sm">
						I'm currently accepting new client projects and collaborations.
						Typical response time is within 24 hours.
					</p>
				</div>
			</div>
		</div>
	</div>
</section>
