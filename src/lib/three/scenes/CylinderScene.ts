import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { ScrollTracker, type ScrollData, lerp, createRandomColor } from '../utils';

interface CylinderElement {
	mesh: THREE.Mesh;
	originalPosition: THREE.Vector3;
	originalScale: THREE.Vector3;
	rotationSpeed: THREE.Vector3;
	floatOffset: number;
	color: THREE.Color;
}

export class CylinderScene {
	private sceneManager: SceneManager;
	private scrollTracker: ScrollTracker;
	private cylinders: CylinderElement[] = [];
	private ambientLight: THREE.AmbientLight;
	private directionalLight: THREE.DirectionalLight;
	private pointLights: THREE.PointLight[] = [];
	private time = 0;

	constructor(canvas: HTMLCanvasElement) {
		this.sceneManager = new SceneManager({
			canvas,
			alpha: true,
			antialias: true
		});

		this.scrollTracker = new ScrollTracker();
		this.setupLights();
		this.createCylinders();
		this.setupScrollInteraction();
		this.startAnimation();
	}

	private setupLights(): void {
		// Ambient light
		this.ambientLight = new THREE.AmbientLight(0x404040, 0.3);
		this.sceneManager.addToScene(this.ambientLight);

		// Main directional light
		this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
		this.directionalLight.position.set(5, 5, 5);
		this.directionalLight.castShadow = true;
		this.sceneManager.addToScene(this.directionalLight);

		// Multiple colored point lights
		const lightColors = [0x3b82f6, 0x8b5cf6, 0x06b6d4, 0x10b981];
		
		lightColors.forEach((color, index) => {
			const pointLight = new THREE.PointLight(color, 0.4, 15);
			const angle = (index / lightColors.length) * Math.PI * 2;
			pointLight.position.set(
				Math.cos(angle) * 8,
				Math.sin(angle) * 3,
				Math.sin(angle) * 8
			);
			this.pointLights.push(pointLight);
			this.sceneManager.addToScene(pointLight);
		});
	}

	private createCylinders(): void {
		const cylinderCount = 20;
		
		// Create different cylinder geometries
		const geometries = [
			new THREE.CylinderGeometry(0.5, 0.5, 2, 8),
			new THREE.CylinderGeometry(0.3, 0.7, 1.5, 6),
			new THREE.CylinderGeometry(0.8, 0.4, 2.5, 12),
			new THREE.CylinderGeometry(0.4, 0.4, 3, 8),
			new THREE.CylinderGeometry(0.6, 0.2, 1.8, 10)
		];

		for (let i = 0; i < cylinderCount; i++) {
			const geometry = geometries[Math.floor(Math.random() * geometries.length)];
			const color = createRandomColor();
			
			const material = new THREE.MeshLambertMaterial({
				color: color,
				transparent: true,
				opacity: 0.8
			});

			const mesh = new THREE.Mesh(geometry, material);
			
			// Position cylinders in a scattered pattern
			const angle = (i / cylinderCount) * Math.PI * 2;
			const radius = 5 + Math.random() * 8;
			const x = Math.cos(angle) * radius + (Math.random() - 0.5) * 4;
			const y = (Math.random() - 0.5) * 10;
			const z = Math.sin(angle) * radius + (Math.random() - 0.5) * 4;
			
			mesh.position.set(x, y, z);
			
			// Random rotation
			mesh.rotation.set(
				Math.random() * Math.PI,
				Math.random() * Math.PI,
				Math.random() * Math.PI
			);
			
			// Random scale
			const scale = 0.5 + Math.random() * 1.5;
			mesh.scale.setScalar(scale);

			const cylinder: CylinderElement = {
				mesh,
				originalPosition: mesh.position.clone(),
				originalScale: mesh.scale.clone(),
				rotationSpeed: new THREE.Vector3(
					(Math.random() - 0.5) * 0.02,
					(Math.random() - 0.5) * 0.02,
					(Math.random() - 0.5) * 0.02
				),
				floatOffset: Math.random() * Math.PI * 2,
				color: color
			};

			this.cylinders.push(cylinder);
			this.sceneManager.addToScene(mesh);
		}

		// Position camera
		this.sceneManager.camera.position.set(0, 0, 15);
		this.sceneManager.camera.lookAt(0, 0, 0);
	}

	private setupScrollInteraction(): void {
		this.scrollTracker.addCallback((data: ScrollData) => {
			// Move camera in a circular motion based on scroll
			const angle = data.scrollProgress * Math.PI * 2;
			const radius = 15 + data.scrollProgress * 5;
			
			this.sceneManager.camera.position.x = Math.sin(angle) * radius;
			this.sceneManager.camera.position.z = Math.cos(angle) * radius;
			this.sceneManager.camera.position.y = data.scrollProgress * 8 - 4;
			this.sceneManager.camera.lookAt(0, 0, 0);

			// Animate cylinders based on scroll
			this.cylinders.forEach((cylinder, index) => {
				const scrollRotation = data.scrollProgress * Math.PI * 4;
				const offset = (index / this.cylinders.length) * Math.PI * 2;
				
				// Rotate cylinders
				cylinder.mesh.rotation.y = scrollRotation + offset;
				
				// Scale cylinders based on scroll
				const scaleMultiplier = 1 + Math.sin(scrollRotation + offset) * 0.3;
				cylinder.mesh.scale.copy(cylinder.originalScale).multiplyScalar(scaleMultiplier);
				
				// Move cylinders in a wave pattern
				const waveY = Math.sin(scrollRotation + offset) * 2;
				cylinder.mesh.position.y = cylinder.originalPosition.y + waveY;
			});
		});
	}

	private startAnimation(): void {
		this.sceneManager.startAnimation((time: number) => {
			this.time = time * 0.001;

			// Animate cylinders
			this.cylinders.forEach((cylinder, index) => {
				// Continuous rotation
				cylinder.mesh.rotation.x += cylinder.rotationSpeed.x;
				cylinder.mesh.rotation.y += cylinder.rotationSpeed.y;
				cylinder.mesh.rotation.z += cylinder.rotationSpeed.z;

				// Floating animation
				const floatY = Math.sin(this.time * 1.5 + cylinder.floatOffset) * 0.3;
				const floatX = Math.cos(this.time * 0.8 + cylinder.floatOffset) * 0.2;
				
				cylinder.mesh.position.x = cylinder.originalPosition.x + floatX;
				cylinder.mesh.position.y = cylinder.originalPosition.y + floatY;

				// Pulsing scale
				const pulseScale = 1 + Math.sin(this.time * 2 + cylinder.floatOffset) * 0.1;
				const currentScale = cylinder.mesh.scale.x;
				cylinder.mesh.scale.setScalar(currentScale * pulseScale);

				// Color animation
				const hueShift = Math.sin(this.time * 0.5 + index * 0.1) * 0.1;
				const material = cylinder.mesh.material as THREE.MeshLambertMaterial;
				const hsl = { h: 0, s: 0, l: 0 };
				cylinder.color.getHSL(hsl);
				material.color.setHSL((hsl.h + hueShift) % 1, hsl.s, hsl.l);
			});

			// Animate point lights
			this.pointLights.forEach((light, index) => {
				const angle = this.time * 0.5 + (index / this.pointLights.length) * Math.PI * 2;
				const radius = 8 + Math.sin(this.time + index) * 2;
				
				light.position.x = Math.cos(angle) * radius;
				light.position.z = Math.sin(angle) * radius;
				light.position.y = Math.sin(this.time * 0.7 + index) * 4;
				
				// Intensity pulsing
				light.intensity = 0.3 + Math.sin(this.time * 2 + index) * 0.2;
			});

			// Animate directional light
			this.directionalLight.position.x = Math.sin(this.time * 0.3) * 10;
			this.directionalLight.position.z = Math.cos(this.time * 0.3) * 10;
		});
	}

	public destroy(): void {
		this.sceneManager.destroy();
		this.scrollTracker.destroy();
		
		// Dispose of geometries and materials
		this.cylinders.forEach((cylinder) => {
			cylinder.mesh.geometry.dispose();
			if (Array.isArray(cylinder.mesh.material)) {
				cylinder.mesh.material.forEach(material => material.dispose());
			} else {
				cylinder.mesh.material.dispose();
			}
		});
	}
}
