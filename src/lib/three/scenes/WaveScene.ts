import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { ScrollTracker, type ScrollData, lerp } from '../utils';

export class WaveScene {
	private sceneManager: SceneManager;
	private scrollTracker: ScrollTracker;
	private waveMesh: THREE.Mesh;
	private waveGeometry: THREE.PlaneGeometry;
	private waveMaterial: THREE.ShaderMaterial;
	private ambientLight: THREE.AmbientLight;
	private directionalLight: THREE.DirectionalLight;
	private time = 0;

	constructor(canvas: HTMLCanvasElement) {
		this.sceneManager = new SceneManager({
			canvas,
			alpha: true,
			antialias: true
		});

		this.scrollTracker = new ScrollTracker();
		this.setupLights();
		this.createWave();
		this.setupScrollInteraction();
		this.startAnimation();
	}

	private setupLights(): void {
		// Ambient light
		this.ambientLight = new THREE.AmbientLight(0x404040, 0.4);
		this.sceneManager.addToScene(this.ambientLight);

		// Directional light
		this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
		this.directionalLight.position.set(5, 5, 5);
		this.sceneManager.addToScene(this.directionalLight);
	}

	private createWave(): void {
		// Create wave geometry
		this.waveGeometry = new THREE.PlaneGeometry(20, 20, 100, 100);

		// Custom shader material for wave effect
		this.waveMaterial = new THREE.ShaderMaterial({
			uniforms: {
				time: { value: 0 },
				color1: { value: new THREE.Color(0x3b82f6) },
				color2: { value: new THREE.Color(0x8b5cf6) },
				color3: { value: new THREE.Color(0x06b6d4) },
				opacity: { value: 0.8 }
			},
			vertexShader: `
				uniform float time;
				varying vec2 vUv;
				varying float vElevation;
				
				void main() {
					vUv = uv;
					
					vec4 modelPosition = modelMatrix * vec4(position, 1.0);
					
					// Create wave patterns
					float elevation = sin(modelPosition.x * 0.3 + time) * 0.5;
					elevation += sin(modelPosition.z * 0.2 + time * 1.5) * 0.3;
					elevation += sin(modelPosition.x * 0.1 + modelPosition.z * 0.1 + time * 0.5) * 0.8;
					
					modelPosition.y += elevation;
					vElevation = elevation;
					
					vec4 viewPosition = viewMatrix * modelPosition;
					vec4 projectedPosition = projectionMatrix * viewPosition;
					
					gl_Position = projectedPosition;
				}
			`,
			fragmentShader: `
				uniform vec3 color1;
				uniform vec3 color2;
				uniform vec3 color3;
				uniform float opacity;
				varying vec2 vUv;
				varying float vElevation;
				
				void main() {
					// Create color gradient based on elevation and UV
					float mixStrength = (vElevation + 1.0) * 0.5;
					vec3 color = mix(color1, color2, mixStrength);
					color = mix(color, color3, vUv.x);
					
					// Add some variation based on UV coordinates
					color += sin(vUv.x * 10.0) * 0.1;
					color += cos(vUv.y * 8.0) * 0.1;
					
					gl_FragColor = vec4(color, opacity);
				}
			`,
			transparent: true,
			side: THREE.DoubleSide
		});

		// Create wave mesh
		this.waveMesh = new THREE.Mesh(this.waveGeometry, this.waveMaterial);
		this.waveMesh.rotation.x = -Math.PI * 0.3;
		this.waveMesh.position.y = -2;
		this.sceneManager.addToScene(this.waveMesh);

		// Create additional wave layers for depth
		for (let i = 1; i <= 3; i++) {
			const layerGeometry = new THREE.PlaneGeometry(15 + i * 2, 15 + i * 2, 80, 80);
			const layerMaterial = new THREE.ShaderMaterial({
				uniforms: {
					time: { value: 0 },
					color1: { value: new THREE.Color(0x1e40af) },
					color2: { value: new THREE.Color(0x7c3aed) },
					color3: { value: new THREE.Color(0x0891b2) },
					opacity: { value: 0.3 / i }
				},
				vertexShader: this.waveMaterial.vertexShader,
				fragmentShader: this.waveMaterial.fragmentShader,
				transparent: true,
				side: THREE.DoubleSide
			});

			const layerMesh = new THREE.Mesh(layerGeometry, layerMaterial);
			layerMesh.rotation.x = -Math.PI * 0.3;
			layerMesh.position.y = -2 - i * 0.5;
			layerMesh.position.z = -i * 0.5;
			this.sceneManager.addToScene(layerMesh);
		}

		// Position camera
		this.sceneManager.camera.position.set(0, 3, 8);
		this.sceneManager.camera.lookAt(0, -1, 0);
	}

	private setupScrollInteraction(): void {
		this.scrollTracker.addCallback((data: ScrollData) => {
			// Move camera based on scroll
			const targetY = 3 + data.scrollProgress * 2;
			const targetZ = 8 - data.scrollProgress * 3;
			
			this.sceneManager.camera.position.y = lerp(
				this.sceneManager.camera.position.y,
				targetY,
				0.1
			);
			this.sceneManager.camera.position.z = lerp(
				this.sceneManager.camera.position.z,
				targetZ,
				0.1
			);

			// Rotate wave based on scroll
			this.waveMesh.rotation.z = data.scrollProgress * Math.PI * 0.2;
			
			// Update wave colors based on scroll
			const hue1 = (data.scrollProgress * 0.3) % 1;
			const hue2 = (data.scrollProgress * 0.3 + 0.3) % 1;
			const hue3 = (data.scrollProgress * 0.3 + 0.6) % 1;
			
			this.waveMaterial.uniforms.color1.value.setHSL(hue1, 0.7, 0.6);
			this.waveMaterial.uniforms.color2.value.setHSL(hue2, 0.7, 0.6);
			this.waveMaterial.uniforms.color3.value.setHSL(hue3, 0.7, 0.6);
		});
	}

	private startAnimation(): void {
		this.sceneManager.startAnimation((time: number) => {
			this.time = time * 0.001;

			// Update wave animation
			this.waveMaterial.uniforms.time.value = this.time;

			// Update all wave layers
			this.sceneManager.scene.traverse((object) => {
				if (object instanceof THREE.Mesh && object.material instanceof THREE.ShaderMaterial) {
					if (object.material.uniforms.time) {
						object.material.uniforms.time.value = this.time;
					}
				}
			});

			// Animate camera with subtle movement
			this.sceneManager.camera.position.x = Math.sin(this.time * 0.2) * 0.5;
			
			// Animate lights
			this.directionalLight.position.x = Math.sin(this.time * 0.5) * 3;
			this.directionalLight.position.z = Math.cos(this.time * 0.5) * 3;
			this.directionalLight.intensity = 0.6 + Math.sin(this.time * 2) * 0.2;
		});
	}

	public destroy(): void {
		this.sceneManager.destroy();
		this.scrollTracker.destroy();
		
		// Dispose of geometries and materials
		this.waveGeometry.dispose();
		this.waveMaterial.dispose();
		
		this.sceneManager.scene.traverse((object) => {
			if (object instanceof THREE.Mesh) {
				object.geometry.dispose();
				if (Array.isArray(object.material)) {
					object.material.forEach(material => material.dispose());
				} else {
					object.material.dispose();
				}
			}
		});
	}
}
