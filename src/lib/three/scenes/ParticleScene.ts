import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { MouseTracker, type MouseData, lerp } from '../utils';

export class ParticleScene {
	private sceneManager: SceneManager;
	private mouseTracker: MouseTracker;
	private particles: THREE.Points;
	private particleGeometry: THREE.BufferGeometry;
	private particleMaterial: THREE.PointsMaterial;
	private particleCount = 1000;
	private positions: Float32Array;
	private velocities: Float32Array;
	private originalPositions: Float32Array;
	private time = 0;
	private mousePosition = { x: 0, y: 0 };

	constructor(canvas: HTMLCanvasElement) {
		this.sceneManager = new SceneManager({
			canvas,
			alpha: true,
			antialias: true
		});

		this.mouseTracker = new MouseTracker();
		this.createParticles();
		this.setupMouseInteraction();
		this.startAnimation();
	}

	private createParticles(): void {
		this.particleGeometry = new THREE.BufferGeometry();
		
		// Create positions array
		this.positions = new Float32Array(this.particleCount * 3);
		this.velocities = new Float32Array(this.particleCount * 3);
		this.originalPositions = new Float32Array(this.particleCount * 3);

		// Initialize particle positions
		for (let i = 0; i < this.particleCount; i++) {
			const i3 = i * 3;
			
			// Random positions in a sphere
			const radius = Math.random() * 15 + 5;
			const theta = Math.random() * Math.PI * 2;
			const phi = Math.acos(Math.random() * 2 - 1);
			
			const x = radius * Math.sin(phi) * Math.cos(theta);
			const y = radius * Math.sin(phi) * Math.sin(theta);
			const z = radius * Math.cos(phi);
			
			this.positions[i3] = x;
			this.positions[i3 + 1] = y;
			this.positions[i3 + 2] = z;
			
			// Store original positions
			this.originalPositions[i3] = x;
			this.originalPositions[i3 + 1] = y;
			this.originalPositions[i3 + 2] = z;
			
			// Initialize velocities
			this.velocities[i3] = (Math.random() - 0.5) * 0.02;
			this.velocities[i3 + 1] = (Math.random() - 0.5) * 0.02;
			this.velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;
		}

		this.particleGeometry.setAttribute('position', new THREE.BufferAttribute(this.positions, 3));

		// Create particle material
		this.particleMaterial = new THREE.PointsMaterial({
			color: 0x64b5f6,
			size: 2,
			transparent: true,
			opacity: 0.8,
			blending: THREE.AdditiveBlending,
			sizeAttenuation: true
		});

		// Create particle system
		this.particles = new THREE.Points(this.particleGeometry, this.particleMaterial);
		this.sceneManager.addToScene(this.particles);

		// Position camera
		this.sceneManager.camera.position.set(0, 0, 20);
		this.sceneManager.camera.lookAt(0, 0, 0);
	}

	private setupMouseInteraction(): void {
		this.mouseTracker.addCallback((data: MouseData) => {
			// Convert mouse position to 3D space
			this.mousePosition.x = data.normalizedX * 10;
			this.mousePosition.y = data.normalizedY * 10;
		});
	}

	private updateParticles(): void {
		const positions = this.particleGeometry.attributes.position.array as Float32Array;
		
		for (let i = 0; i < this.particleCount; i++) {
			const i3 = i * 3;
			
			// Get current position
			const x = positions[i3];
			const y = positions[i3 + 1];
			const z = positions[i3 + 2];
			
			// Calculate distance to mouse
			const dx = this.mousePosition.x - x;
			const dy = this.mousePosition.y - y;
			const distance = Math.sqrt(dx * dx + dy * dy);
			
			// Mouse repulsion effect
			if (distance < 5) {
				const force = (5 - distance) / 5;
				this.velocities[i3] -= dx * force * 0.001;
				this.velocities[i3 + 1] -= dy * force * 0.001;
			}
			
			// Attraction to original position
			const originalX = this.originalPositions[i3];
			const originalY = this.originalPositions[i3 + 1];
			const originalZ = this.originalPositions[i3 + 2];
			
			this.velocities[i3] += (originalX - x) * 0.001;
			this.velocities[i3 + 1] += (originalY - y) * 0.001;
			this.velocities[i3 + 2] += (originalZ - z) * 0.001;
			
			// Apply velocity damping
			this.velocities[i3] *= 0.98;
			this.velocities[i3 + 1] *= 0.98;
			this.velocities[i3 + 2] *= 0.98;
			
			// Update positions
			positions[i3] += this.velocities[i3];
			positions[i3 + 1] += this.velocities[i3 + 1];
			positions[i3 + 2] += this.velocities[i3 + 2];
			
			// Add floating motion
			positions[i3 + 1] += Math.sin(this.time + i * 0.01) * 0.01;
			positions[i3] += Math.cos(this.time + i * 0.01) * 0.005;
		}
		
		this.particleGeometry.attributes.position.needsUpdate = true;
	}

	private startAnimation(): void {
		this.sceneManager.startAnimation((time: number) => {
			this.time = time * 0.001;
			
			this.updateParticles();
			
			// Rotate the entire particle system slowly
			this.particles.rotation.y += 0.001;
			this.particles.rotation.x = Math.sin(this.time * 0.5) * 0.1;
			
			// Animate material properties
			this.particleMaterial.opacity = 0.6 + Math.sin(this.time * 2) * 0.2;
			
			// Color cycling
			const hue = (this.time * 0.1) % 1;
			this.particleMaterial.color.setHSL(hue, 0.7, 0.6);
		});
	}

	public destroy(): void {
		this.sceneManager.destroy();
		this.mouseTracker.destroy();
		
		// Dispose of geometry and material
		this.particleGeometry.dispose();
		this.particleMaterial.dispose();
	}
}
