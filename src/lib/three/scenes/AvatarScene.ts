import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { ScrollTracker, type ScrollData, lerp, createRandomColor } from '../utils';

interface AvatarPart {
	mesh: THREE.Mesh;
	originalPosition: THREE.Vector3;
	originalRotation: THREE.Euler;
	animationOffset: number;
}

export class AvatarScene {
	private sceneManager: SceneManager;
	private scrollTracker: ScrollTracker;
	private avatarGroup: THREE.Group;
	private avatarParts: AvatarPart[] = [];
	private ambientLight: THREE.AmbientLight;
	private directionalLight: THREE.DirectionalLight;
	private pointLight: THREE.PointLight;
	private time = 0;

	constructor(canvas: HTMLCanvasElement) {
		this.sceneManager = new SceneManager({
			canvas,
			alpha: true,
			antialias: true
		});

		this.scrollTracker = new ScrollTracker();
		this.setupLights();
		this.createAvatar();
		this.setupScrollInteraction();
		this.startAnimation();
	}

	private setupLights(): void {
		// Ambient light
		this.ambientLight = new THREE.AmbientLight(0x404040, 0.4);
		this.sceneManager.addToScene(this.ambientLight);

		// Main directional light
		this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
		this.directionalLight.position.set(5, 5, 5);
		this.directionalLight.castShadow = true;
		this.sceneManager.addToScene(this.directionalLight);

		// Point light for accent
		this.pointLight = new THREE.PointLight(0x64b5f6, 0.6, 10);
		this.pointLight.position.set(-3, 2, 3);
		this.sceneManager.addToScene(this.pointLight);
	}

	private createAvatar(): void {
		this.avatarGroup = new THREE.Group();
		
		// Head (sphere)
		const headGeometry = new THREE.SphereGeometry(1, 32, 32);
		const headMaterial = new THREE.MeshLambertMaterial({ color: 0xfdbcb4 });
		const head = new THREE.Mesh(headGeometry, headMaterial);
		head.position.set(0, 2, 0);
		this.avatarGroup.add(head);
		this.avatarParts.push({
			mesh: head,
			originalPosition: head.position.clone(),
			originalRotation: head.rotation.clone(),
			animationOffset: 0
		});

		// Eyes (small spheres)
		const eyeGeometry = new THREE.SphereGeometry(0.1, 16, 16);
		const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });
		
		const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
		leftEye.position.set(-0.3, 2.2, 0.8);
		this.avatarGroup.add(leftEye);
		
		const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
		rightEye.position.set(0.3, 2.2, 0.8);
		this.avatarGroup.add(rightEye);

		// Body (cylinder)
		const bodyGeometry = new THREE.CylinderGeometry(0.8, 1, 2, 8);
		const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x3b82f6 });
		const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
		body.position.set(0, 0, 0);
		this.avatarGroup.add(body);
		this.avatarParts.push({
			mesh: body,
			originalPosition: body.position.clone(),
			originalRotation: body.rotation.clone(),
			animationOffset: Math.PI / 4
		});

		// Arms (cylinders)
		const armGeometry = new THREE.CylinderGeometry(0.2, 0.2, 1.5, 8);
		const armMaterial = new THREE.MeshLambertMaterial({ color: 0xfdbcb4 });
		
		const leftArm = new THREE.Mesh(armGeometry, armMaterial);
		leftArm.position.set(-1.2, 0.5, 0);
		leftArm.rotation.z = Math.PI / 6;
		this.avatarGroup.add(leftArm);
		this.avatarParts.push({
			mesh: leftArm,
			originalPosition: leftArm.position.clone(),
			originalRotation: leftArm.rotation.clone(),
			animationOffset: Math.PI / 2
		});
		
		const rightArm = new THREE.Mesh(armGeometry, armMaterial);
		rightArm.position.set(1.2, 0.5, 0);
		rightArm.rotation.z = -Math.PI / 6;
		this.avatarGroup.add(rightArm);
		this.avatarParts.push({
			mesh: rightArm,
			originalPosition: rightArm.position.clone(),
			originalRotation: rightArm.rotation.clone(),
			animationOffset: Math.PI / 2 + Math.PI
		});

		// Legs (cylinders)
		const legGeometry = new THREE.CylinderGeometry(0.25, 0.25, 1.5, 8);
		const legMaterial = new THREE.MeshLambertMaterial({ color: 0x1f2937 });
		
		const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
		leftLeg.position.set(-0.4, -1.75, 0);
		this.avatarGroup.add(leftLeg);
		this.avatarParts.push({
			mesh: leftLeg,
			originalPosition: leftLeg.position.clone(),
			originalRotation: leftLeg.rotation.clone(),
			animationOffset: Math.PI
		});
		
		const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
		rightLeg.position.set(0.4, -1.75, 0);
		this.avatarGroup.add(rightLeg);
		this.avatarParts.push({
			mesh: rightLeg,
			originalPosition: rightLeg.position.clone(),
			originalRotation: rightLeg.rotation.clone(),
			animationOffset: Math.PI + Math.PI / 2
		});

		// Decorative elements (floating primitives)
		const decorativeGeometries = [
			new THREE.BoxGeometry(0.3, 0.3, 0.3),
			new THREE.OctahedronGeometry(0.2),
			new THREE.TetrahedronGeometry(0.25)
		];

		for (let i = 0; i < 6; i++) {
			const geometry = decorativeGeometries[i % decorativeGeometries.length];
			const material = new THREE.MeshLambertMaterial({ 
				color: createRandomColor(),
				transparent: true,
				opacity: 0.7
			});
			
			const decorative = new THREE.Mesh(geometry, material);
			const angle = (i / 6) * Math.PI * 2;
			const radius = 3 + Math.random() * 2;
			
			decorative.position.set(
				Math.cos(angle) * radius,
				Math.random() * 4 - 2,
				Math.sin(angle) * radius
			);
			
			this.avatarGroup.add(decorative);
			this.avatarParts.push({
				mesh: decorative,
				originalPosition: decorative.position.clone(),
				originalRotation: decorative.rotation.clone(),
				animationOffset: i * Math.PI / 3
			});
		}

		this.sceneManager.addToScene(this.avatarGroup);

		// Position camera
		this.sceneManager.camera.position.set(0, 0, 8);
		this.sceneManager.camera.lookAt(0, 0, 0);
	}

	private setupScrollInteraction(): void {
		this.scrollTracker.addCallback((data: ScrollData) => {
			// Rotate avatar based on scroll
			const rotationY = data.scrollProgress * Math.PI * 2;
			this.avatarGroup.rotation.y = rotationY;
			
			// Tilt avatar slightly based on scroll
			this.avatarGroup.rotation.x = Math.sin(data.scrollProgress * Math.PI) * 0.2;
			
			// Move camera position based on scroll
			const cameraRadius = 8 + data.scrollProgress * 3;
			const cameraAngle = data.scrollProgress * Math.PI * 0.5;
			
			this.sceneManager.camera.position.x = Math.sin(cameraAngle) * cameraRadius;
			this.sceneManager.camera.position.z = Math.cos(cameraAngle) * cameraRadius;
			this.sceneManager.camera.position.y = data.scrollProgress * 2;
			this.sceneManager.camera.lookAt(0, 0, 0);
		});
	}

	private startAnimation(): void {
		this.sceneManager.startAnimation((time: number) => {
			this.time = time * 0.001;

			// Animate avatar parts
			this.avatarParts.forEach((part, index) => {
				// Floating animation
				const floatY = Math.sin(this.time * 2 + part.animationOffset) * 0.1;
				part.mesh.position.y = part.originalPosition.y + floatY;

				// Subtle rotation
				part.mesh.rotation.y = part.originalRotation.y + Math.sin(this.time + part.animationOffset) * 0.1;
				part.mesh.rotation.x = part.originalRotation.x + Math.cos(this.time * 0.7 + part.animationOffset) * 0.05;

				// Scale pulsing for decorative elements
				if (index >= 6) { // Decorative elements start at index 6
					const scale = 1 + Math.sin(this.time * 3 + part.animationOffset) * 0.1;
					part.mesh.scale.setScalar(scale);
				}
			});

			// Animate lights
			this.pointLight.position.x = Math.sin(this.time) * 3;
			this.pointLight.position.z = Math.cos(this.time) * 3;
			this.pointLight.intensity = 0.4 + Math.sin(this.time * 2) * 0.2;

			// Color cycling for point light
			const hue = (this.time * 0.1) % 1;
			this.pointLight.color.setHSL(hue, 0.7, 0.6);
		});
	}

	public destroy(): void {
		this.sceneManager.destroy();
		this.scrollTracker.destroy();
		
		// Dispose of geometries and materials
		this.avatarGroup.traverse((object) => {
			if (object instanceof THREE.Mesh) {
				object.geometry.dispose();
				if (Array.isArray(object.material)) {
					object.material.forEach(material => material.dispose());
				} else {
					object.material.dispose();
				}
			}
		});
	}
}
