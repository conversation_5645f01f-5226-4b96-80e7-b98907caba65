import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { SceneManager } from '../SceneManager';
import { ScrollTracker, type ScrollData, lerp, createRandomColor } from '../utils';

interface DecorativePart {
	mesh: THREE.Mesh;
	originalPosition: THREE.Vector3;
	originalRotation: THREE.Euler;
	animationOffset: number;
}

export class AvatarScene {
	private sceneManager: SceneManager;
	private scrollTracker: ScrollTracker;
	private avatarModel: THREE.Group | null = null;
	private decorativeParts: DecorativePart[] = [];
	private ambientLight: THREE.AmbientLight;
	private directionalLight: THREE.DirectionalLight;
	private pointLight: THREE.PointLight;
	private gltfLoader: GLTFLoader;
	private time = 0;
	private isModelLoaded = false;

	constructor(canvas: HTMLCanvasElement) {
		this.sceneManager = new SceneManager({
			canvas,
			alpha: true,
			antialias: true
		});

		this.scrollTracker = new ScrollTracker();
		this.gltfLoader = new GLTFLoader();
		this.setupLights();
		this.loadAvatarModel();
		this.createDecorativeElements();
		this.setupScrollInteraction();
		this.startAnimation();
	}

	private setupLights(): void {
		// Ambient light
		this.ambientLight = new THREE.AmbientLight(0x404040, 0.4);
		this.sceneManager.addToScene(this.ambientLight);

		// Main directional light
		this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
		this.directionalLight.position.set(5, 5, 5);
		this.directionalLight.castShadow = true;
		this.sceneManager.addToScene(this.directionalLight);

		// Point light for accent
		this.pointLight = new THREE.PointLight(0x64b5f6, 0.6, 10);
		this.pointLight.position.set(-3, 2, 3);
		this.sceneManager.addToScene(this.pointLight);
	}

	private async loadAvatarModel(): Promise<void> {
		try {
			const gltf = await this.gltfLoader.loadAsync('/models/Male_Suit.glb');
			this.avatarModel = gltf.scene;

			// Scale and position the model
			this.avatarModel.scale.setScalar(2);
			this.avatarModel.position.set(0, -5, 0);

			// Ensure all materials receive lighting properly
			this.avatarModel.traverse((child) => {
				if (child instanceof THREE.Mesh) {
					child.castShadow = true;
					child.receiveShadow = true;

					// If the material is not already a Lambert or Phong material, convert it
					if (child.material instanceof THREE.MeshStandardMaterial) {
						// Keep the standard material but ensure it works well with our lighting
						child.material.needsUpdate = true;
					}
				}
			});

			this.sceneManager.addToScene(this.avatarModel);
			this.isModelLoaded = true;

			console.log('Avatar model loaded successfully');
		} catch (error) {
			console.error('Error loading avatar model:', error);
			// Fallback: create a simple placeholder
			this.createFallbackAvatar();
		}

		// Position camera
		this.sceneManager.camera.position.set(0, 0, 8);
		this.sceneManager.camera.lookAt(0, 0, 0);
	}

	private createFallbackAvatar(): void {
		// Simple fallback avatar if GLB fails to load
		const geometry = new THREE.CapsuleGeometry(0.5, 2, 4, 8);
		const material = new THREE.MeshLambertMaterial({ color: 0x3b82f6 });
		const fallbackAvatar = new THREE.Mesh(geometry, material);
		fallbackAvatar.position.set(0, 0, 0);

		this.avatarModel = new THREE.Group();
		this.avatarModel.add(fallbackAvatar);
		this.sceneManager.addToScene(this.avatarModel);
		this.isModelLoaded = true;
	}

	private createDecorativeElements(): void {
		// Decorative elements (floating primitives)
		const decorativeGeometries = [
			new THREE.BoxGeometry(0.3, 0.3, 0.3),
			new THREE.OctahedronGeometry(0.2),
			new THREE.TetrahedronGeometry(0.25)
		];

		for (let i = 0; i < 6; i++) {
			const geometry = decorativeGeometries[i % decorativeGeometries.length];
			const material = new THREE.MeshLambertMaterial({
				color: createRandomColor(),
				transparent: true,
				opacity: 0.7
			});

			const decorative = new THREE.Mesh(geometry, material);
			const angle = (i / 6) * Math.PI * 2;
			const radius = 4 + Math.random() * 2;

			decorative.position.set(
				Math.cos(angle) * radius,
				Math.random() * 4 - 2,
				Math.sin(angle) * radius
			);

			this.decorativeParts.push({
				mesh: decorative,
				originalPosition: decorative.position.clone(),
				originalRotation: decorative.rotation.clone(),
				animationOffset: i * Math.PI / 3
			});

			this.sceneManager.addToScene(decorative);
		}
	}

	private setupScrollInteraction(): void {
		this.scrollTracker.addCallback((data: ScrollData) => {
			// Rotate avatar based on scroll
			if (this.avatarModel && this.isModelLoaded) {
				const rotationY = data.scrollProgress * Math.PI * 2;
				this.avatarModel.rotation.y = rotationY;

				// Tilt avatar slightly based on scroll
				this.avatarModel.rotation.x = Math.sin(data.scrollProgress * Math.PI) * 0.1;

				// Subtle floating motion
				this.avatarModel.position.y = -2 + Math.sin(data.scrollProgress * Math.PI * 2) * 0.3;
			}

			// Move camera position based on scroll
			const cameraRadius = 8 + data.scrollProgress * 3;
			const cameraAngle = data.scrollProgress * Math.PI * 0.5;

			this.sceneManager.camera.position.x = Math.sin(cameraAngle) * cameraRadius;
			this.sceneManager.camera.position.z = Math.cos(cameraAngle) * cameraRadius;
			this.sceneManager.camera.position.y = data.scrollProgress * 2;
			this.sceneManager.camera.lookAt(0, 0, 0);
		});
	}

	private startAnimation(): void {
		this.sceneManager.startAnimation((time: number) => {
			this.time = time * 0.001;

			// Animate avatar model with subtle movements
			if (this.avatarModel && this.isModelLoaded) {
				// Gentle breathing-like animation
				const breathingScale = 1 + Math.sin(this.time * 1.5) * 0.02;
				this.avatarModel.scale.setScalar(2 * breathingScale);

				// Subtle idle rotation
				this.avatarModel.rotation.y += Math.sin(this.time * 0.5) * 0.001;
			}

			// Animate decorative parts
			this.decorativeParts.forEach((part) => {
				// Floating animation
				const floatY = Math.sin(this.time * 2 + part.animationOffset) * 0.1;
				part.mesh.position.y = part.originalPosition.y + floatY;

				// Subtle rotation
				part.mesh.rotation.y = part.originalRotation.y + Math.sin(this.time + part.animationOffset) * 0.1;
				part.mesh.rotation.x = part.originalRotation.x + Math.cos(this.time * 0.7 + part.animationOffset) * 0.05;

				// Scale pulsing
				const scale = 1 + Math.sin(this.time * 3 + part.animationOffset) * 0.1;
				part.mesh.scale.setScalar(scale);
			});

			// Animate lights
			this.pointLight.position.x = Math.sin(this.time) * 3;
			this.pointLight.position.z = Math.cos(this.time) * 3;
			this.pointLight.intensity = 0.4 + Math.sin(this.time * 2) * 0.2;

			// Color cycling for point light
			const hue = (this.time * 0.1) % 1;
			this.pointLight.color.setHSL(hue, 0.7, 0.6);
		});
	}

	public destroy(): void {
		this.sceneManager.destroy();
		this.scrollTracker.destroy();

		// Dispose of avatar model resources
		if (this.avatarModel) {
			this.avatarModel.traverse((object) => {
				if (object instanceof THREE.Mesh) {
					object.geometry.dispose();
					if (Array.isArray(object.material)) {
						object.material.forEach(material => material.dispose());
					} else {
						object.material.dispose();
					}
				}
			});
		}

		// Dispose of decorative elements
		this.decorativeParts.forEach((part) => {
			part.mesh.geometry.dispose();
			if (Array.isArray(part.mesh.material)) {
				part.mesh.material.forEach(material => material.dispose());
			} else {
				part.mesh.material.dispose();
			}
		});
	}
}
