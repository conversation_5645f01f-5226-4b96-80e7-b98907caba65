import * as THREE from 'three';

export interface ScrollData {
	scrollY: number;
	scrollProgress: number;
	direction: 'up' | 'down';
}

export interface MouseData {
	x: number;
	y: number;
	normalizedX: number; // -1 to 1
	normalizedY: number; // -1 to 1
}

export class ScrollTracker {
	private callbacks: ((data: ScrollData) => void)[] = [];
	private lastScrollY = 0;
	private isListening = false;

	public addCallback(callback: (data: ScrollData) => void): void {
		this.callbacks.push(callback);
		if (!this.isListening) {
			this.startListening();
		}
	}

	public removeCallback(callback: (data: ScrollData) => void): void {
		const index = this.callbacks.indexOf(callback);
		if (index > -1) {
			this.callbacks.splice(index, 1);
		}
		if (this.callbacks.length === 0) {
			this.stopListening();
		}
	}

	private startListening(): void {
		this.isListening = true;
		window.addEventListener('scroll', this.handleScroll, { passive: true });
	}

	private stopListening(): void {
		this.isListening = false;
		window.removeEventListener('scroll', this.handleScroll);
	}

	private handleScroll = (): void => {
		const scrollY = window.scrollY;
		const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
		const scrollProgress = Math.min(scrollY / maxScroll, 1);
		const direction = scrollY > this.lastScrollY ? 'down' : 'up';

		const data: ScrollData = {
			scrollY,
			scrollProgress,
			direction
		};

		this.callbacks.forEach(callback => callback(data));
		this.lastScrollY = scrollY;
	};

	public destroy(): void {
		this.stopListening();
		this.callbacks = [];
	}
}

export class MouseTracker {
	private callbacks: ((data: MouseData) => void)[] = [];
	private isListening = false;

	public addCallback(callback: (data: MouseData) => void): void {
		this.callbacks.push(callback);
		if (!this.isListening) {
			this.startListening();
		}
	}

	public removeCallback(callback: (data: MouseData) => void): void {
		const index = this.callbacks.indexOf(callback);
		if (index > -1) {
			this.callbacks.splice(index, 1);
		}
		if (this.callbacks.length === 0) {
			this.stopListening();
		}
	}

	private startListening(): void {
		this.isListening = true;
		window.addEventListener('mousemove', this.handleMouseMove, { passive: true });
	}

	private stopListening(): void {
		this.isListening = false;
		window.removeEventListener('mousemove', this.handleMouseMove);
	}

	private handleMouseMove = (event: MouseEvent): void => {
		const data: MouseData = {
			x: event.clientX,
			y: event.clientY,
			normalizedX: (event.clientX / window.innerWidth) * 2 - 1,
			normalizedY: -(event.clientY / window.innerHeight) * 2 + 1
		};

		this.callbacks.forEach(callback => callback(data));
	};

	public destroy(): void {
		this.stopListening();
		this.callbacks = [];
	}
}

// Utility functions for common 3D operations
export const lerp = (start: number, end: number, factor: number): number => {
	return start + (end - start) * factor;
};

export const clamp = (value: number, min: number, max: number): number => {
	return Math.min(Math.max(value, min), max);
};

export const mapRange = (
	value: number,
	inMin: number,
	inMax: number,
	outMin: number,
	outMax: number
): number => {
	return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
};

export const easeInOutCubic = (t: number): number => {
	return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};

export const createRandomColor = (): THREE.Color => {
	const colors = [
		0x3b82f6, // blue-500
		0x8b5cf6, // violet-500
		0x06b6d4, // cyan-500
		0x10b981, // emerald-500
		0xf59e0b, // amber-500
		0xef4444, // red-500
		0xec4899, // pink-500
		0x84cc16  // lime-500
	];
	return new THREE.Color(colors[Math.floor(Math.random() * colors.length)]);
};

export const createGradientTexture = (
	color1: THREE.Color,
	color2: THREE.Color,
	width = 256,
	height = 256
): THREE.Texture => {
	const canvas = document.createElement('canvas');
	canvas.width = width;
	canvas.height = height;
	
	const context = canvas.getContext('2d')!;
	const gradient = context.createLinearGradient(0, 0, 0, height);
	
	gradient.addColorStop(0, `#${color1.getHexString()}`);
	gradient.addColorStop(1, `#${color2.getHexString()}`);
	
	context.fillStyle = gradient;
	context.fillRect(0, 0, width, height);
	
	const texture = new THREE.CanvasTexture(canvas);
	texture.needsUpdate = true;
	
	return texture;
};
