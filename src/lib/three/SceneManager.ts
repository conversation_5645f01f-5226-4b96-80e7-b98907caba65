import * as THREE from 'three';

export interface SceneConfig {
	canvas: HTMLCanvasElement;
	alpha?: boolean;
	antialias?: boolean;
	powerPreference?: 'default' | 'high-performance' | 'low-power';
}

export class SceneManager {
	public scene: THREE.Scene;
	public camera: THREE.PerspectiveCamera;
	public renderer: THREE.WebGLRenderer;
	public canvas: HTMLCanvasElement;
	private animationId: number | null = null;
	private resizeObserver: ResizeObserver | null = null;
	private isDestroyed = false;

	constructor(config: SceneConfig) {
		this.canvas = config.canvas;
		
		// Initialize scene
		this.scene = new THREE.Scene();
		
		// Initialize camera
		this.camera = new THREE.PerspectiveCamera(
			75,
			this.canvas.clientWidth / this.canvas.clientHeight,
			0.1,
			1000
		);
		
		// Initialize renderer
		this.renderer = new THREE.WebGLRenderer({
			canvas: this.canvas,
			alpha: config.alpha ?? true,
			antialias: config.antialias ?? true,
			powerPreference: config.powerPreference ?? 'high-performance'
		});
		
		this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
		this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
		
		// Set up resize observer
		this.setupResizeObserver();
	}

	private setupResizeObserver(): void {
		this.resizeObserver = new ResizeObserver((entries) => {
			for (const entry of entries) {
				const { width, height } = entry.contentRect;
				this.handleResize(width, height);
			}
		});
		
		this.resizeObserver.observe(this.canvas);
	}

	private handleResize(width: number, height: number): void {
		if (this.isDestroyed) return;
		
		// Update camera aspect ratio
		this.camera.aspect = width / height;
		this.camera.updateProjectionMatrix();
		
		// Update renderer size
		this.renderer.setSize(width, height);
		this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
	}

	public startAnimation(animateCallback: (time: number) => void): void {
		if (this.isDestroyed) return;
		
		const animate = (time: number) => {
			if (this.isDestroyed) return;
			
			animateCallback(time);
			this.renderer.render(this.scene, this.camera);
			this.animationId = requestAnimationFrame(animate);
		};
		
		this.animationId = requestAnimationFrame(animate);
	}

	public stopAnimation(): void {
		if (this.animationId !== null) {
			cancelAnimationFrame(this.animationId);
			this.animationId = null;
		}
	}

	public addToScene(object: THREE.Object3D): void {
		this.scene.add(object);
	}

	public removeFromScene(object: THREE.Object3D): void {
		this.scene.remove(object);
	}

	public destroy(): void {
		this.isDestroyed = true;
		this.stopAnimation();
		
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
			this.resizeObserver = null;
		}
		
		// Dispose of Three.js resources
		this.scene.traverse((object) => {
			if (object instanceof THREE.Mesh) {
				object.geometry.dispose();
				if (Array.isArray(object.material)) {
					object.material.forEach(material => material.dispose());
				} else {
					object.material.dispose();
				}
			}
		});
		
		this.renderer.dispose();
	}
}
